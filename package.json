{"name": "library-management-system-ai", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "start:separated": "ng serve --configuration=separated", "build": "ng build", "build:prod": "ng build --configuration=production", "build:separated": "ng build --configuration=separated", "watch": "ng build --watch --configuration development", "test": "ng test", "backend:start": "cd backend-api && npm start", "backend:dev": "cd backend-api && npm run dev", "backend:install": "cd backend-api && npm install", "dev:full": "concurrently \"npm run backend:start\" \"npm start\"", "dev:separated": "concurrently \"npm run backend:start\" \"npm run start:separated\"", "setup": "npm install && npm run backend:install"}, "prettier": {"overrides": [{"files": "*.html", "options": {"parser": "angular"}}]}, "private": true, "dependencies": {"@angular/common": "^20.0.6", "@angular/compiler": "^20.0.6", "@angular/core": "^20.0.6", "@angular/forms": "^20.0.6", "@angular/platform-browser": "^20.0.6", "@angular/router": "^20.0.6", "postcss": "^8.5.6", "rxjs": "~7.8.0", "tailwindcss": "^3.4.0", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular/build": "^20.0.5", "@angular/cli": "^20.0.5", "@angular/compiler-cli": "^20.0.6", "@types/jasmine": "~5.1.0", "concurrently": "^9.1.0", "dotenv": "^17.1.0", "jasmine-core": "~5.7.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.8.2"}}